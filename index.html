<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pooristan: From Dust to Dominance</title>
    <link rel="stylesheet" href="css/styles.css">
</head>
<body class="game-page">
    <div class="game-container">
        <!-- Header with user info and game title -->
        <header class="game-header">
            <div class="game-title">
                <h1>Pooristan</h1>
                <p class="subtitle">From Dust to Dominance</p>
            </div>
            <div class="user-info" id="user-info">
                <span id="user-name">Player</span>
                <button id="logout-btn" class="logout-btn">Logout</button>
            </div>
        </header>

        <!-- Main game area -->
        <main class="game-main">
            <!-- GDP Display and Click Area -->
            <section class="gdp-section">
                <div class="gdp-display">
                    <h2>GDP: $<span id="gdp-amount">0</span></h2>
                    <p>Per Second: $<span id="gdp-per-second">0</span></p>
                </div>
                <button id="gdp-click-btn" class="gdp-click-btn">
                    Click for GDP!
                </button>
            </section>

            <!-- Tabbed Interface -->
            <section class="game-tabs">
                <div class="tab-buttons">
                    <button class="tab-btn active" data-tab="resources">Resources</button>
                    <button class="tab-btn" data-tab="upgrades">Upgrades</button>
                    <button class="tab-btn" data-tab="events">Events</button>
                    <button class="tab-btn" data-tab="tech">Tech Tree</button>
                </div>

                <!-- Tab Content Areas -->
                <div class="tab-content">
                    <div id="resources-tab" class="tab-panel active">
                        <h3>Resources</h3>
                        <div class="resource-list" id="resource-list">
                            <!-- Resources will be populated by JavaScript -->
                        </div>
                    </div>

                    <div id="upgrades-tab" class="tab-panel">
                        <h3>Generators & Upgrades</h3>
                        <div class="generator-list" id="generator-list">
                            <!-- Generators will be populated by JavaScript -->
                        </div>
                    </div>

                    <div id="events-tab" class="tab-panel">
                        <h3>Events</h3>
                        <div class="event-list" id="event-list">
                            <p>No active events</p>
                        </div>
                    </div>

                    <div id="tech-tab" class="tab-panel">
                        <h3>Technology Tree</h3>
                        <div class="tech-tree" id="tech-tree">
                            <!-- Tech tree will be populated by JavaScript -->
                        </div>
                    </div>
                </div>
            </section>
        </main>

        <!-- Game Status Bar -->
        <footer class="game-footer">
            <div class="game-stats">
                <span>Total Clicks: <span id="total-clicks">0</span></span>
                <span>Play Time: <span id="play-time">00:00</span></span>
                <span>Phase: <span id="game-phase">1</span></span>
            </div>
        </footer>
    </div>

    <!-- Event Modal -->
    <div id="event-modal" class="modal hidden">
        <div class="modal-content">
            <h3 id="event-title"></h3>
            <p id="event-description"></p>
            <div id="event-choices" class="event-choices">
                <!-- Event choices will be populated by JavaScript -->
            </div>
        </div>
    </div>

    <!-- Loading Screen -->
    <div id="loading-screen" class="loading-screen">
        <div class="loading-content">
            <h2>Loading Pooristan...</h2>
            <div class="loading-spinner"></div>
        </div>
    </div>

    <!-- JavaScript Modules -->
    <script src="js/utils.js"></script>
    <script src="js/orangeSDK.js"></script>
    <script src="js/orangeID.js"></script>
    <script src="js/audioManager.js"></script>
    <script src="js/animationManager.js"></script>
    <script src="js/gameEngine.js"></script>
    <script src="js/technologyManager.js"></script>
    <script src="js/eventManager.js"></script>
    <script src="js/uiManager.js"></script>
    <script src="js/main.js"></script>
    
    <!-- Authentication Check Script -->
    <script>
        // Check authentication before initializing game
        document.addEventListener('DOMContentLoaded', () => {
            // Initialize Orange ID manager
            const orangeID = new OrangeIDManager();
            window.orangeIDInstance = orangeID;
            
            // Initialize authentication
            orangeID.initializeAuth().then((success) => {
                if (!success) {
                    console.warn('Authentication initialization failed');
                }
                
                // Check if user is authenticated
                if (!orangeID.isAuthenticated()) {
                    console.log('User not authenticated, redirecting to login');
                    window.location.href = 'login.html';
                    return;
                }
                
                // User is authenticated, display user info
                const user = orangeID.getCurrentUser();
                if (user) {
                    updateUserDisplay(user);
                }
                
                // Set up logout button
                const logoutBtn = document.getElementById('logout-btn');
                if (logoutBtn) {
                    logoutBtn.addEventListener('click', async () => {
                        try {
                            await orangeID.signOut();
                            window.location.href = 'login.html';
                        } catch (error) {
                            console.error('Logout failed:', error);
                            // Force redirect anyway
                            window.location.href = 'login.html';
                        }
                    });
                }
                
                // Set up auth state change listener
                orangeID.onAuthStateChanged((user) => {
                    if (!user) {
                        // User signed out, redirect to login
                        window.location.href = 'login.html';
                    } else {
                        // User signed in, update display
                        updateUserDisplay(user);
                    }
                });
                
            }).catch((error) => {
                console.error('Authentication initialization error:', error);
                // Redirect to login on error
                window.location.href = 'login.html';
            });
        });
        
        /**
         * Update user display in the header
         */
        function updateUserDisplay(user) {
            const userNameElement = document.getElementById('user-name');
            if (userNameElement && user) {
                // Display user's display name or name
                const displayName = user.displayName || user.name || 'Player';
                userNameElement.textContent = displayName;
                
                // Add user profile picture if available
                if (user.picture) {
                    const userInfo = document.getElementById('user-info');
                    if (userInfo) {
                        // Check if profile picture already exists
                        let profilePic = userInfo.querySelector('.profile-pic');
                        if (!profilePic) {
                            profilePic = document.createElement('img');
                            profilePic.className = 'profile-pic';
                            profilePic.style.width = '32px';
                            profilePic.style.height = '32px';
                            profilePic.style.borderRadius = '50%';
                            profilePic.style.marginRight = '0.5rem';
                            userInfo.insertBefore(profilePic, userNameElement);
                        }
                        profilePic.src = user.picture;
                        profilePic.alt = displayName + ' profile picture';
                    }
                }
            }
        }
        
        /**
         * Handle authentication errors
         */
        function handleAuthError(error) {
            console.error('Authentication error:', error);
            
            // Show error message to user
            const errorDiv = document.createElement('div');
            errorDiv.className = 'auth-error';
            errorDiv.textContent = 'Authentication error occurred. Please try logging in again.';
            errorDiv.style.position = 'fixed';
            errorDiv.style.top = '20px';
            errorDiv.style.right = '20px';
            errorDiv.style.zIndex = '1000';
            errorDiv.style.padding = '1rem';
            errorDiv.style.background = 'rgba(231, 76, 60, 0.9)';
            errorDiv.style.color = 'white';
            errorDiv.style.borderRadius = '5px';
            
            document.body.appendChild(errorDiv);
            
            // Auto-remove error message after 5 seconds
            setTimeout(() => {
                if (errorDiv.parentNode) {
                    errorDiv.parentNode.removeChild(errorDiv);
                }
            }, 5000);
            
            // Redirect to login after showing error
            setTimeout(() => {
                window.location.href = 'login.html';
            }, 2000);
        }
    </script>
</body>
</html>