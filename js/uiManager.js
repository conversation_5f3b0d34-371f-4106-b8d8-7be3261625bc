/**
 * UIManager.js - User Interface Management Module
 * Handles all DOM manipulation and user interface updates
 * Requirements: 4.1, 4.2, 4.3, 4.4, 4.5
 */

class UIManager {
    constructor(gameEngine) {
        this.gameEngine = gameEngine;
        this.orangeSDK = null;
        this.audioManager = null;
        this.animationManager = null;
        this.elements = {};
        this.currentTab = 'resources';
        this.updateInterval = null;
        this.lastUpdateTime = 0;
        this.updateFrequency = 100; // Update UI every 100ms
    }

    /**
     * Set Orange SDK reference
     */
    setOrangeSDK(orangeSDK) {
        this.orangeSDK = orangeSDK;
    }

    /**
     * Set Audio Manager reference
     * Requirements: 12.1, 12.3, 12.5
     */
    setAudioManager(audioManager) {
        this.audioManager = audioManager;
    }

    /**
     * Set Animation Manager reference
     * Requirements: 12.2, 12.4, 12.5
     */
    setAnimationManager(animationManager) {
        this.animationManager = animationManager;
    }

    /**
     * Initialize UI Manager
     * Requirements: 4.1, 4.2, 4.3, 4.4, 4.5
     */
    initialize() {
        console.log('UIManager: Initializing...');
        
        // Cache DOM elements
        this.cacheElements();
        
        // Setup event listeners
        this.setupEventListeners();
        
        // Initialize displays
        this.updateAllDisplays();
        
        // Start periodic updates
        this.startPeriodicUpdates();
        
        console.log('UIManager: Initialized successfully');
    }

    /**
     * Cache frequently used DOM elements
     */
    cacheElements() {
        this.elements = {
            // GDP elements
            gdpAmount: DOMUtils.getElementById('gdp-amount'),
            gdpPerSecond: DOMUtils.getElementById('gdp-per-second'),
            gdpClickBtn: DOMUtils.getElementById('gdp-click-btn'),
            
            // User info
            userName: DOMUtils.getElementById('user-name'),
            userInfo: DOMUtils.getElementById('user-info'),
            
            // Tab elements
            tabButtons: document.querySelectorAll('.tab-btn'),
            tabPanels: document.querySelectorAll('.tab-panel'),
            
            // Content areas
            resourceList: DOMUtils.getElementById('resource-list'),
            generatorList: DOMUtils.getElementById('generator-list'),
            eventList: DOMUtils.getElementById('event-list'),
            techTree: DOMUtils.getElementById('tech-tree'),
            
            // Stats
            totalClicks: DOMUtils.getElementById('total-clicks'),
            playTime: DOMUtils.getElementById('play-time'),
            gamePhase: DOMUtils.getElementById('game-phase'),
            
            // Modal
            eventModal: DOMUtils.getElementById('event-modal'),
            eventTitle: DOMUtils.getElementById('event-title'),
            eventDescription: DOMUtils.getElementById('event-description'),
            eventChoices: DOMUtils.getElementById('event-choices'),
            
            // Loading screen
            loadingScreen: DOMUtils.getElementById('loading-screen')
        };
    }

    /**
     * Setup event listeners
     * Requirements: 4.4
     */
    setupEventListeners() {
        // GDP click button
        if (this.elements.gdpClickBtn) {
            this.elements.gdpClickBtn.addEventListener('click', () => {
                this.handleGDPClick();
            });
        }

        // Tab buttons
        this.elements.tabButtons.forEach(button => {
            button.addEventListener('click', () => {
                const tabName = button.getAttribute('data-tab');
                this.switchTab(tabName);
            });
        });

        // User info setup
        this.setupUserInfo();
        
        // Add keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            // Space or Enter to click GDP
            if ((e.key === ' ' || e.key === 'Enter') && document.activeElement === document.body) {
                e.preventDefault();
                this.handleGDPClick();
            }
            
            // Number keys 1-4 to switch tabs
            if (e.key >= '1' && e.key <= '4' && document.activeElement === document.body) {
                const tabIndex = parseInt(e.key) - 1;
                if (tabIndex >= 0 && tabIndex < this.elements.tabButtons.length) {
                    this.elements.tabButtons[tabIndex].click();
                }
            }
        });
    }

    /**
     * Setup user information display
     */
    setupUserInfo() {
        // Get user from Orange ID if available
        if (typeof OrangeIDManager !== 'undefined') {
            const orangeID = new OrangeIDManager();
            const user = orangeID.getCurrentUser();
            
            if (user && this.elements.userName) {
                DOMUtils.setText(this.elements.userName, user.name || 'Player');
                
                // Add user profile picture if available
                if (user.picture && this.elements.userInfo) {
                    const profilePic = DOMUtils.createElement('img', {
                        className: 'profile-pic',
                        src: user.picture,
                        alt: `${user.name || 'Player'} profile picture`
                    });
                    
                    profilePic.style.width = '32px';
                    profilePic.style.height = '32px';
                    profilePic.style.borderRadius = '50%';
                    profilePic.style.marginRight = '0.5rem';
                    
                    // Check if profile pic already exists
                    const existingPic = this.elements.userInfo.querySelector('.profile-pic');
                    if (!existingPic) {
                        this.elements.userInfo.insertBefore(profilePic, this.elements.userName);
                    }
                }
            }
        }
    }

    /**
     * Handle GDP click
     * Requirements: 1.1, 1.2, 1.3, 4.4, 12.1, 12.4
     */
    handleGDPClick() {
        if (this.gameEngine) {
            const newGDP = this.gameEngine.clickGDP();

            if (newGDP !== undefined) {
                // Play click sound
                if (this.audioManager) {
                    this.audioManager.playClickSound();
                }

                // Add click animation
                DOMUtils.addClass(this.elements.gdpClickBtn, 'click-animation', 300);

                // Create floating number animation using AnimationManager
                if (this.animationManager) {
                    this.animationManager.animateResourceGain('gdp', 1, this.elements.gdpClickBtn);
                } else {
                    // Fallback to old floating number method
                    this.createFloatingNumber('+1', this.elements.gdpClickBtn);
                }

                // Update display immediately
                this.updateGDPDisplay();
                this.updateGameStats();
            }
        }
    }

    /**
     * Create floating number animation
     * Requirements: 4.4
     */
    createFloatingNumber(text, targetElement) {
        if (!targetElement) return;
        
        const rect = targetElement.getBoundingClientRect();
        const floatingNumber = DOMUtils.createElement('div', {
            className: 'floating-number'
        }, text);
        
        // Style the floating number
        Object.assign(floatingNumber.style, {
            position: 'absolute',
            left: `${rect.left + rect.width / 2}px`,
            top: `${rect.top + rect.height / 2}px`,
            transform: 'translate(-50%, -50%)',
            color: '#2ecc71',
            fontWeight: 'bold',
            fontSize: '1.2rem',
            pointerEvents: 'none',
            zIndex: '1000',
            textShadow: '0 0 3px rgba(0,0,0,0.5)',
            animation: 'float-up 1s ease-out forwards'
        });
        
        // Add animation keyframes if they don't exist
        if (!document.querySelector('#floating-animation')) {
            const style = document.createElement('style');
            style.id = 'floating-animation';
            style.textContent = `
                @keyframes float-up {
                    0% { opacity: 1; transform: translate(-50%, -50%); }
                    100% { opacity: 0; transform: translate(-50%, -150%); }
                }
            `;
            document.head.appendChild(style);
        }
        
        // Add to document and remove after animation
        document.body.appendChild(floatingNumber);
        setTimeout(() => {
            if (floatingNumber.parentNode) {
                floatingNumber.parentNode.removeChild(floatingNumber);
            }
        }, 1000);
    }

    /**
     * Switch between tabs
     * Requirements: 4.3, 11.2, 11.4
     */
    switchTab(tabName) {
        // Update tab buttons
        this.elements.tabButtons.forEach(button => {
            const isActive = button.getAttribute('data-tab') === tabName;
            button.classList.toggle('active', isActive);
        });

        // Update tab panels
        this.elements.tabPanels.forEach(panel => {
            const isActive = panel.id === `${tabName}-tab`;
            panel.classList.toggle('active', isActive);
        });

        this.currentTab = tabName;

        // Clear notification badge for the selected tab
        this.removeTabNotificationBadge(tabName);

        // Update the content of the newly activated tab
        this.updateTabContent(tabName);

        // Provide visual feedback for tab switch
        this.showTabSwitchFeedback(tabName);
    }
    
    /**
     * Update content of the current tab
     * Requirements: 4.1, 4.2, 4.3, 8.3, 11.1
     */
    updateTabContent(tabName) {
        switch (tabName) {
            case 'resources':
                this.updateResourceDisplays();
                break;
            case 'upgrades':
                this.updateGeneratorDisplays();
                break;
            case 'events':
                this.updateEventDisplays();
                break;
            case 'tech':
                this.updateTechnologyTreeDisplay();
                break;
        }
    }

    /**
     * Update GDP display
     * Requirements: 4.1
     */
    updateGDPDisplay() {
        if (!this.gameEngine) return;
        
        const gameState = this.gameEngine.getGameState();
        
        if (this.elements.gdpAmount) {
            DOMUtils.setText(this.elements.gdpAmount, NumberFormatter.format(gameState.gdp));
        }
        
        if (this.elements.gdpPerSecond) {
            DOMUtils.setText(this.elements.gdpPerSecond, NumberFormatter.format(gameState.gdpPerSecond));
        }
    }

    /**
     * Update resource displays
     * Requirements: 4.1, 4.2, 7.1, 7.2
     */
    updateResourceDisplays() {
        if (!this.gameEngine || !this.elements.resourceList) return;
        
        const resources = this.gameEngine.getAllResourcesInfo();
        let resourceHTML = '';
        
        // Display GDP first as the primary resource
        resourceHTML += `
            <div class="resource-item">
                <h4>GDP</h4>
                <div class="resource-details">
                    <div class="resource-amount">
                        <span class="label">Amount:</span>
                        <span class="value">${NumberFormatter.format(resources.gdp.amount)}</span>
                    </div>
                    <div class="resource-rate">
                        <span class="label">Per Second:</span>
                        <span class="value">${NumberFormatter.format(resources.gdp.perSecond)}</span>
                    </div>
                    <div class="resource-total">
                        <span class="label">Total Earned:</span>
                        <span class="value">${NumberFormatter.format(resources.gdp.totalEarned)}</span>
                    </div>
                </div>
            </div>
        `;
        
        // Display secondary resources
        const resourceNames = {
            food: 'Food',
            industry: 'Industry',
            knowledge: 'Knowledge',
            influence: 'Influence',
            bitcoin: 'Bitcoin'
        };
        
        const resourceColors = {
            food: '#27ae60',      // Green
            industry: '#e74c3c',  // Red
            knowledge: '#3498db', // Blue
            influence: '#f39c12', // Orange
            bitcoin: '#f1c40f'    // Yellow
        };
        
        for (const resourceType in resources) {
            // Skip GDP as it's already displayed
            if (resourceType === 'gdp') continue;
            
            const resource = resources[resourceType];
            const displayName = resourceNames[resourceType] || resourceType;
            const color = resourceColors[resourceType] || '#bdc3c7';
            
            resourceHTML += `
                <div class="resource-item" style="border-left-color: ${color};">
                    <h4>${displayName}</h4>
                    <div class="resource-details">
                        <div class="resource-amount">
                            <span class="label">Amount:</span>
                            <span class="value">${NumberFormatter.format(resource.amount)}</span>
                        </div>
                        <div class="resource-rate">
                            <span class="label">Per Second:</span>
                            <span class="value">${NumberFormatter.format(resource.perSecond)}</span>
                        </div>
                    </div>
                </div>
            `;
        }
        
        DOMUtils.setHTML(this.elements.resourceList, resourceHTML);
    }

    /**
     * Update generator displays
     * Requirements: 4.2, 4.3, 4.5, 7.2, 7.3
     */
    updateGeneratorDisplays() {
        if (!this.gameEngine || !this.elements.generatorList) return;

        let generatorHTML = '';

        // Get all unlocked generators
        const unlockedGenerators = this.gameEngine.getUnlockedGenerators();

        // Generator display names and descriptions
        const generatorConfig = {
            farm: {
                name: 'Farm',
                description: 'Produces food and basic GDP',
                icon: '🚜'
            },
            factory: {
                name: 'Factory',
                description: 'Produces industrial goods',
                icon: '🏭'
            },
            school: {
                name: 'School',
                description: 'Generates knowledge through education',
                icon: '🏫'
            },
            embassy: {
                name: 'Embassy',
                description: 'Builds international influence',
                icon: '🏛️'
            },
            bitcoinMiner: {
                name: 'Bitcoin Miner',
                description: 'Mines cryptocurrency for the nation',
                icon: '₿'
            }
        };

        // Display each unlocked generator
        for (const generatorType in unlockedGenerators) {
            const generatorInfo = unlockedGenerators[generatorType];
            const config = generatorConfig[generatorType];

            if (!config) continue;

            // Format cost display
            const costDisplay = this.formatResourceCost(generatorInfo.cost);
            const upgradeCostDisplay = this.formatResourceCost(generatorInfo.upgradeCost);

            generatorHTML += `
                <div class="generator-item">
                    <h4>${config.icon} ${config.name}</h4>
                    <p class="generator-description">${config.description}</p>
                    <div class="generator-details">
                        <div class="generator-count">
                            <span class="label">Owned:</span>
                            <span class="value">${generatorInfo.count}</span>
                        </div>
                        <div class="generator-production">
                            <span class="label">Production:</span>
                            <span class="value">${NumberFormatter.format(generatorInfo.production)} ${generatorInfo.resourceType}/s</span>
                        </div>
                    </div>

                    <div class="generator-purchase">
                        <div class="purchase-info">
                            <span class="label">Cost:</span>
                            <span class="value">${costDisplay}</span>
                        </div>
                        <div class="purchase-buttons">
                            <button class="purchase-btn ${generatorInfo.canAffordPurchase ? '' : 'disabled'}"
                                data-type="${generatorType}" data-quantity="1"
                                ${generatorInfo.canAffordPurchase ? '' : 'disabled'}>
                                Buy 1
                            </button>

                            <button class="purchase-btn ${generatorInfo.canAffordPurchase ? '' : 'disabled'}"
                                data-type="${generatorType}" data-quantity="10"
                                ${generatorInfo.canAffordPurchase ? '' : 'disabled'}>
                                Buy 10
                            </button>

                            <button class="purchase-btn ${generatorInfo.canAffordPurchase ? '' : 'disabled'}"
                                data-type="${generatorType}" data-quantity="max"
                                ${generatorInfo.canAffordPurchase ? '' : 'disabled'}>
                                Buy Max
                            </button>
                        </div>
                    </div>

                    <div class="generator-upgrade">
                        <div class="upgrade-info">
                            <span class="label">Upgrade Level:</span>
                            <span class="value">${generatorInfo.upgradeLevel}</span>
                        </div>
                        <div class="upgrade-cost">
                            <span class="label">Upgrade Cost:</span>
                            <span class="value">${upgradeCostDisplay}</span>
                        </div>
                        <button class="upgrade-btn ${generatorInfo.canAffordUpgrade ? '' : 'disabled'}"
                            data-type="${generatorType}"
                            ${generatorInfo.canAffordUpgrade ? '' : 'disabled'}>
                            Upgrade ${config.name}
                        </button>
                    </div>
                </div>
            `;
        }

        DOMUtils.setHTML(this.elements.generatorList, generatorHTML);

        // Add event listeners to the newly created buttons
        this.setupGeneratorButtons();
    }

    /**
     * Format resource cost for display
     * Requirements: 7.4
     */
    formatResourceCost(cost) {
        if (!cost || typeof cost !== 'object') {
            return '0';
        }

        const costParts = [];
        const resourceNames = {
            gdp: 'GDP',
            food: 'Food',
            industry: 'Industry',
            knowledge: 'Knowledge',
            influence: 'Influence',
            bitcoin: 'Bitcoin'
        };

        for (const resourceType in cost) {
            const amount = cost[resourceType];
            if (amount > 0) {
                const displayName = resourceNames[resourceType] || resourceType;
                costParts.push(`${NumberFormatter.format(amount)} ${displayName}`);
            }
        }

        return costParts.length > 0 ? costParts.join(', ') : '0';
    }

    /**
     * Update technology tree display
     * Requirements: 8.3
     */
    updateTechnologyTreeDisplay() {
        if (!this.gameEngine || !this.gameEngine.technologyManager || !this.elements.techTree) return;

        const technologyManager = this.gameEngine.technologyManager;
        const treeData = technologyManager.getTechnologyTreeForDisplay();
        const currentResearch = technologyManager.getCurrentResearchStatus();

        let techHTML = '';

        // Show current research progress if any
        if (currentResearch) {
            const progressPercent = Math.floor(currentResearch.progress * 100);
            const timeRemaining = Math.ceil(currentResearch.timeRemaining / 1000);

            techHTML += `
                <div class="current-research">
                    <h4>Current Research</h4>
                    <div class="research-item">
                        <div class="research-name">${currentResearch.name}</div>
                        <div class="research-progress">
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: ${progressPercent}%"></div>
                            </div>
                            <div class="progress-text">${progressPercent}% (${timeRemaining}s remaining)</div>
                        </div>
                    </div>
                </div>
            `;
        }

        // Group technologies by tier
        const tiers = {};
        for (const [techId, tech] of Object.entries(treeData)) {
            if (!tiers[tech.tier]) {
                tiers[tech.tier] = [];
            }
            tiers[tech.tier].push({ id: techId, ...tech });
        }

        // Display technologies by tier
        for (let tier = 1; tier <= 5; tier++) {
            if (!tiers[tier]) continue;

            techHTML += `
                <div class="tech-tier">
                    <h4>Tier ${tier} Technologies</h4>
                    <div class="tech-grid">
            `;

            tiers[tier].forEach(tech => {
                const statusClass = this.getTechnologyStatusClass(tech);
                const buttonText = this.getTechnologyButtonText(tech);
                const isDisabled = !tech.canResearch || tech.isInProgress;

                techHTML += `
                    <div class="tech-card ${statusClass}">
                        <div class="tech-header">
                            <h5 class="tech-name">${tech.name}</h5>
                            <div class="tech-tier-badge">T${tech.tier}</div>
                        </div>
                        <div class="tech-description">${tech.description}</div>
                        <div class="tech-cost">
                            <strong>Cost:</strong> ${this.formatResourceCost(tech.cost)}
                        </div>
                        ${tech.prerequisites.length > 0 ? `
                            <div class="tech-prerequisites">
                                <strong>Requires:</strong> ${this.formatPrerequisites(tech.prerequisites, treeData)}
                            </div>
                        ` : ''}
                        ${tech.unlocks.length > 0 ? `
                            <div class="tech-unlocks">
                                <strong>Unlocks:</strong> ${tech.unlocks.join(', ')}
                            </div>
                        ` : ''}
                        <div class="tech-actions">
                            <button
                                class="research-btn ${statusClass}"
                                data-tech-id="${tech.id}"
                                ${isDisabled ? 'disabled' : ''}
                            >
                                ${buttonText}
                            </button>
                        </div>
                        ${tech.isInProgress ? `
                            <div class="tech-progress">
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: ${Math.floor(tech.progress * 100)}%"></div>
                                </div>
                            </div>
                        ` : ''}
                    </div>
                `;
            });

            techHTML += `
                    </div>
                </div>
            `;
        }

        if (techHTML === '') {
            techHTML = '<p>No technologies available yet. Gain knowledge to unlock research!</p>';
        }

        DOMUtils.setHTML(this.elements.techTree, techHTML);

        // Add event listeners to research buttons
        this.setupTechnologyButtons();
    }

    /**
     * Get CSS class for technology status
     * Requirements: 8.3
     */
    getTechnologyStatusClass(tech) {
        if (tech.isResearched) return 'researched';
        if (tech.isInProgress) return 'in-progress';
        if (tech.canResearch) return 'available';
        if (tech.isAvailable) return 'unlocked';
        return 'locked';
    }

    /**
     * Get button text for technology
     * Requirements: 8.3
     */
    getTechnologyButtonText(tech) {
        if (tech.isResearched) return 'Researched';
        if (tech.isInProgress) return 'Researching...';
        if (tech.canResearch) return 'Research';
        if (tech.isAvailable) return 'Cannot Afford';
        return 'Locked';
    }

    /**
     * Format prerequisites for display
     * Requirements: 8.3
     */
    formatPrerequisites(prerequisites, treeData) {
        return prerequisites.map(prereqId => {
            const prereq = treeData[prereqId];
            return prereq ? prereq.name : prereqId;
        }).join(', ');
    }

    /**
     * Setup event listeners for technology buttons
     * Requirements: 8.1, 8.3
     */
    setupTechnologyButtons() {
        const researchButtons = document.querySelectorAll('.research-btn');

        researchButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                const techId = e.target.getAttribute('data-tech-id');
                if (techId) {
                    this.handleTechnologyResearch(techId);
                }
            });
        });
    }

    /**
     * Handle technology research button click
     * Requirements: 8.1, 8.2
     */
    handleTechnologyResearch(technologyId) {
        if (!this.gameEngine || !this.gameEngine.technologyManager) return;

        const technologyManager = this.gameEngine.technologyManager;
        const success = technologyManager.startResearch(technologyId);

        if (success) {
            const technology = technologyManager.technologyTree[technologyId];

            // Show research started animation
            const button = document.querySelector(`.research-btn[data-tech-id="${technologyId}"]`);
            if (button) {
                DOMUtils.addClass(button, 'research-animation', 500);
                this.showButtonFeedback(button, 'info');
            }

            // Show notification
            this.showNotification(`Research started: ${technology.name}`, 'success');

            // Update displays
            this.updateTechnologyTreeDisplay();
            this.updateAllDisplays();
        } else {
            // Show error notification and feedback
            const button = document.querySelector(`.research-btn[data-tech-id="${technologyId}"]`);
            if (button) {
                this.showButtonFeedback(button, 'error');
            }
            this.showNotification('Cannot start research!', 'error');
        }
    }
    
    /**
     * Setup generator purchase and upgrade buttons
     * Requirements: 4.3, 4.4, 4.5
     */
    setupGeneratorButtons() {
        // Purchase buttons
        const purchaseButtons = document.querySelectorAll('.purchase-btn');
        purchaseButtons.forEach(button => {
            if (button.getAttribute('data-listener') === 'true') return;
            
            button.setAttribute('data-listener', 'true');
            button.addEventListener('click', () => {
                const type = button.getAttribute('data-type');
                const quantityAttr = button.getAttribute('data-quantity');
                
                let quantity = 1;
                if (quantityAttr === 'max') {
                    quantity = this.gameEngine.getMaxAffordableGenerators(type);
                } else {
                    quantity = parseInt(quantityAttr) || 1;
                }
                
                this.handleGeneratorPurchase(type, quantity);
            });
        });
        
        // Upgrade buttons
        const upgradeButtons = document.querySelectorAll('.upgrade-btn');
        upgradeButtons.forEach(button => {
            if (button.getAttribute('data-listener') === 'true') return;
            
            button.setAttribute('data-listener', 'true');
            button.addEventListener('click', () => {
                const type = button.getAttribute('data-type');
                this.handleGeneratorUpgrade(type);
            });
        });
    }
    
    /**
     * Handle generator purchase
     * Requirements: 2.1, 2.5, 4.4, 4.5, 11.4
     */
    handleGeneratorPurchase(type, quantity) {
        if (!this.gameEngine) return;

        const success = this.gameEngine.purchaseMultipleGenerators(type, quantity);

        if (success) {
            // Play purchase sound
            if (this.audioManager) {
                this.audioManager.playPurchaseSound();
            }

            // Show purchase animation
            const button = document.querySelector(`.purchase-btn[data-type="${type}"][data-quantity="${quantity}"]`);
            if (button) {
                DOMUtils.addClass(button, 'purchase-animation', 500);
                this.showButtonFeedback(button, 'success');

                // Use AnimationManager for purchase animation
                if (this.animationManager) {
                    this.animationManager.animatePurchase(type);
                }

                // Create floating text showing how many were purchased
                this.createFloatingNumber(`+${quantity}`, button);
            }

            // Update displays
            this.updateAllDisplays();
        } else {
            // Play error sound
            if (this.audioManager) {
                this.audioManager.playEventSound('error');
            }

            // Show error notification and feedback
            const button = document.querySelector(`.purchase-btn[data-type="${type}"][data-quantity="${quantity}"]`);
            if (button) {
                this.showButtonFeedback(button, 'error');
            }
            this.showNotification('Cannot afford this purchase!', 'error');
        }
    }
    
    /**
     * Handle generator upgrade
     * Requirements: 2.3, 2.4, 4.4, 4.5, 11.4
     */
    handleGeneratorUpgrade(type) {
        if (!this.gameEngine) return;

        const success = this.gameEngine.upgradeGenerator(type);

        if (success) {
            // Play upgrade sound
            if (this.audioManager) {
                this.audioManager.playUpgradeSound();
            }

            // Show upgrade animation
            const button = document.querySelector(`.upgrade-btn[data-type="${type}"]`);
            if (button) {
                DOMUtils.addClass(button, 'purchase-animation', 500);
                this.showButtonFeedback(button, 'success');

                // Use AnimationManager for upgrade animation
                if (this.animationManager) {
                    this.animationManager.animateUpgrade(type);
                }

                // Create floating text showing upgrade
                this.createFloatingNumber('Upgraded!', button);
            }

            // Update displays
            this.updateAllDisplays();
        } else {
            // Play error sound
            if (this.audioManager) {
                this.audioManager.playEventSound('error');
            }

            // Show error notification and feedback
            const button = document.querySelector(`.upgrade-btn[data-type="${type}"]`);
            if (button) {
                this.showButtonFeedback(button, 'error');
            }
            this.showNotification('Cannot afford this upgrade!', 'error');
        }
    }

    /**
     * Update game stats
     */
    updateGameStats() {
        if (!this.gameEngine) return;
        
        const gameState = this.gameEngine.getGameState();
        
        if (this.elements.totalClicks) {
            DOMUtils.setText(this.elements.totalClicks, NumberFormatter.format(gameState.totalClicks));
        }
        
        if (this.elements.playTime) {
            DOMUtils.setText(this.elements.playTime, TimeUtils.formatTime(gameState.totalPlayTime));
        }
        
        if (this.elements.gamePhase) {
            DOMUtils.setText(this.elements.gamePhase, gameState.gamePhase || '1');
        }
    }

    /**
     * Update all displays
     * Requirements: 4.1, 4.2, 4.3, 8.3, 11.1, 11.3
     */
    updateAllDisplays() {
        this.updateGDPDisplay();
        this.updateResourceDisplays();
        this.updateGeneratorDisplays();
        this.updateTechnologyTreeDisplay();
        this.updateEventDisplays();
        this.updateGameStats();
        this.updateTabNotifications();
    }

    /**
     * Update displays (called by game engine)
     * Requirements: 4.1, 4.2
     */
    updateDisplays() {
        const now = performance.now();
        
        // Limit update frequency to avoid performance issues
        if (now - this.lastUpdateTime < this.updateFrequency) {
            return;
        }
        
        this.lastUpdateTime = now;
        
        // Only update GDP and stats frequently, other displays less often
        this.updateGDPDisplay();
        this.updateGameStats();
    }

    /**
     * Start periodic UI updates
     */
    startPeriodicUpdates() {
        // Update resource and generator displays every 5 seconds
        this.updateInterval = setInterval(() => {
            this.updateResourceDisplays();
            this.updateGeneratorDisplays();
            this.updateTechnologyTreeDisplay();
        }, 5000);
    }

    /**
     * Stop periodic updates
     */
    stopPeriodicUpdates() {
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
            this.updateInterval = null;
        }
    }

    /**
     * Show notification
     * Requirements: 4.4, 4.5, 12.3, 12.5
     */
    showNotification(message, type = 'info') {
        // Play notification sound based on type
        if (this.audioManager) {
            switch (type) {
                case 'victory':
                    this.audioManager.playVictorySound();
                    break;
                case 'error':
                    this.audioManager.playEventSound('error');
                    break;
                case 'success':
                    this.audioManager.playNotificationSound();
                    break;
                default:
                    this.audioManager.playNotificationSound();
            }
        }
        // Create notification element if it doesn't exist
        let notification = document.querySelector('.game-notification');
        if (!notification) {
            notification = DOMUtils.createElement('div', {
                className: `game-notification ${type}`
            });
            
            // Style the notification
            Object.assign(notification.style, {
                position: 'fixed',
                bottom: '20px',
                right: '20px',
                padding: '10px 20px',
                borderRadius: '5px',
                color: 'white',
                fontWeight: 'bold',
                zIndex: '1000',
                opacity: '0',
                transition: 'opacity 0.3s ease'
            });
            
            document.body.appendChild(notification);
        } else {
            // Update existing notification
            notification.className = `game-notification ${type}`;
        }
        
        // Set background color based on type
        switch (type) {
            case 'victory':
                notification.style.backgroundColor = 'rgba(255, 215, 0, 0.9)';
                notification.style.color = 'black';
                notification.style.fontSize = '1.2rem';
                break;
            case 'error':
                notification.style.backgroundColor = 'rgba(231, 76, 60, 0.9)';
                break;
            case 'success':
                notification.style.backgroundColor = 'rgba(46, 204, 113, 0.9)';
                break;
            case 'warning':
                notification.style.backgroundColor = 'rgba(243, 156, 18, 0.9)';
                break;
            default:
                notification.style.backgroundColor = 'rgba(52, 152, 219, 0.9)';
        }

        // Set message and show notification
        notification.textContent = message;
        notification.style.opacity = '1';

        // Special animations for victory
        if (type === 'victory' && this.animationManager) {
            this.animationManager.animateMilestone('victory', notification);
        }

        // Hide notification after appropriate time (longer for victory)
        const hideDelay = type === 'victory' ? 8000 : 3000;
        setTimeout(() => {
            notification.style.opacity = '0';

            // Remove from DOM after fade out
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, hideDelay);
    }

    /**
     * Show event modal with event details and choices
     * Requirements: 9.1, 9.5, 12.3
     * @param {Object} event - Event to display
     */
    showEventModal(event) {
        if (!this.elements.eventModal || !event) {
            console.error('UIManager: Cannot show event modal - missing elements or event');
            return;
        }

        console.log('UIManager: Showing event modal for:', event.name);

        // Play event sound
        if (this.audioManager) {
            this.audioManager.playEventSound(event.type || 'default');
        }

        // Animate event appearance
        if (this.animationManager) {
            this.animationManager.animateEvent(event.type || 'default');
        }

        // Set event title and description
        if (this.elements.eventTitle) {
            DOMUtils.setText(this.elements.eventTitle, event.name);
        }

        if (this.elements.eventDescription) {
            DOMUtils.setText(this.elements.eventDescription, event.description);
        }

        // Clear existing choices
        if (this.elements.eventChoices) {
            this.elements.eventChoices.innerHTML = '';

            // Create choice buttons
            event.choices.forEach(choice => {
                const choiceButton = DOMUtils.createElement('button', {
                    className: 'event-choice-btn',
                    textContent: choice.name
                });

                // Add choice description as tooltip or subtitle
                if (choice.description) {
                    const choiceDescription = DOMUtils.createElement('div', {
                        className: 'choice-description',
                        textContent: choice.description
                    });

                    const choiceContainer = DOMUtils.createElement('div', {
                        className: 'choice-container'
                    });

                    choiceContainer.appendChild(choiceButton);
                    choiceContainer.appendChild(choiceDescription);

                    // Add cost information if present
                    if (choice.cost && Object.keys(choice.cost).length > 0) {
                        const costText = this.formatChoiceCost(choice.cost);
                        const costElement = DOMUtils.createElement('div', {
                            className: 'choice-cost',
                            textContent: `Cost: ${costText}`
                        });
                        choiceContainer.appendChild(costElement);
                    }

                    // Check if player can afford this choice
                    const canAfford = this.canPlayerAffordChoice(choice);
                    if (!canAfford) {
                        choiceButton.classList.add('disabled');
                        choiceButton.disabled = true;
                    }

                    // Add click handler
                    choiceButton.addEventListener('click', () => {
                        if (canAfford) {
                            this.handleEventChoice(event.id, choice.id);
                        }
                    });

                    this.elements.eventChoices.appendChild(choiceContainer);
                } else {
                    // Simple button without description
                    const canAfford = this.canPlayerAffordChoice(choice);
                    if (!canAfford) {
                        choiceButton.classList.add('disabled');
                        choiceButton.disabled = true;
                    }

                    choiceButton.addEventListener('click', () => {
                        if (canAfford) {
                            this.handleEventChoice(event.id, choice.id);
                        }
                    });

                    this.elements.eventChoices.appendChild(choiceButton);
                }
            });
        }

        // Show the modal
        this.elements.eventModal.classList.remove('hidden');

        // Add escape key handler
        this.eventModalKeyHandler = (e) => {
            if (e.key === 'Escape') {
                // Don't allow closing event modals with escape - player must make a choice
                console.log('UIManager: Event modals cannot be closed with escape key');
            }
        };
        document.addEventListener('keydown', this.eventModalKeyHandler);
    }

    /**
     * Hide event modal
     * Requirements: 9.1, 9.5
     */
    hideEventModal() {
        if (!this.elements.eventModal) {
            console.error('UIManager: Cannot hide event modal - missing element');
            return;
        }

        console.log('UIManager: Hiding event modal');

        // Hide the modal
        this.elements.eventModal.classList.add('hidden');

        // Clear content
        if (this.elements.eventTitle) {
            DOMUtils.setText(this.elements.eventTitle, '');
        }
        if (this.elements.eventDescription) {
            DOMUtils.setText(this.elements.eventDescription, '');
        }
        if (this.elements.eventChoices) {
            this.elements.eventChoices.innerHTML = '';
        }

        // Remove event listener
        if (this.eventModalKeyHandler) {
            document.removeEventListener('keydown', this.eventModalKeyHandler);
            this.eventModalKeyHandler = null;
        }
    }

    /**
     * Handle event choice selection
     * @param {string} eventId - ID of the event
     * @param {string} choiceId - ID of the chosen option
     */
    handleEventChoice(eventId, choiceId) {
        console.log(`UIManager: Player chose ${choiceId} for event ${eventId}`);

        // Delegate to event manager through game engine
        if (this.gameEngine && this.gameEngine.eventManager) {
            this.gameEngine.eventManager.resolveEvent(eventId, choiceId);
        } else {
            console.error('UIManager: Cannot resolve event - missing event manager');
        }
    }

    /**
     * Update tab notification badges
     * Requirements: 11.3
     */
    updateTabNotifications() {
        if (!this.gameEngine) return;

        // Clear existing badges
        this.clearTabNotifications();

        // Check for events requiring attention
        this.updateEventTabNotifications();

        // Check for available technologies
        this.updateTechTabNotifications();

        // Check for affordable upgrades
        this.updateUpgradeTabNotifications();
    }

    /**
     * Clear all tab notification badges
     */
    clearTabNotifications() {
        const badges = document.querySelectorAll('.tab-notification-badge');
        badges.forEach(badge => badge.remove());
    }

    /**
     * Update event tab notifications
     * Requirements: 11.3
     */
    updateEventTabNotifications() {
        if (!this.gameEngine.eventManager) return;

        const activeEvents = this.gameEngine.eventManager.getActiveEvents();
        const pendingEvents = activeEvents.filter(event => event.isActive && !event.resolved);

        if (pendingEvents.length > 0) {
            this.addTabNotificationBadge('events', pendingEvents.length, 'warning');
        }
    }

    /**
     * Update tech tab notifications
     * Requirements: 11.3
     */
    updateTechTabNotifications() {
        if (!this.gameEngine.technologyManager) return;

        const availableTechs = this.gameEngine.technologyManager.getAvailableTechnologies();
        const affordableTechs = availableTechs.filter(tech =>
            this.gameEngine.technologyManager.canResearch(tech.id)
        );

        if (affordableTechs.length > 0) {
            this.addTabNotificationBadge('tech', affordableTechs.length, 'info');
        }
    }

    /**
     * Update upgrade tab notifications
     * Requirements: 11.3
     */
    updateUpgradeTabNotifications() {
        const gameState = this.gameEngine.getGameState();
        let affordableUpgrades = 0;

        // Check for affordable generator purchases
        for (const [type, generator] of Object.entries(gameState.generators)) {
            const cost = this.gameEngine.getGeneratorCost(type, 1);
            if (this.gameEngine.canAffordCost(cost)) {
                affordableUpgrades++;
            }
        }

        if (affordableUpgrades > 0) {
            this.addTabNotificationBadge('upgrades', affordableUpgrades, 'success');
        }
    }

    /**
     * Add notification badge to a tab
     * Requirements: 11.3
     * @param {string} tabName - Name of the tab
     * @param {number} count - Number to display in badge
     * @param {string} type - Badge type (warning, info, success)
     */
    addTabNotificationBadge(tabName, count, type = 'warning') {
        const tabButton = document.querySelector(`.tab-btn[data-tab="${tabName}"]`);
        if (!tabButton) return;

        // Remove existing badge if any
        const existingBadge = tabButton.querySelector('.tab-notification-badge');
        if (existingBadge) {
            existingBadge.remove();
        }

        // Create new badge
        const badge = DOMUtils.createElement('span', {
            className: `tab-notification-badge ${type}`,
            textContent: count > 99 ? '99+' : count.toString()
        });

        tabButton.appendChild(badge);
    }

    /**
     * Remove notification badge from a tab
     * Requirements: 11.3
     * @param {string} tabName - Name of the tab
     */
    removeTabNotificationBadge(tabName) {
        const tabButton = document.querySelector(`.tab-btn[data-tab="${tabName}"]`);
        if (!tabButton) return;

        const badge = tabButton.querySelector('.tab-notification-badge');
        if (badge) {
            badge.remove();
        }
    }

    /**
     * Show visual feedback for tab switching
     * Requirements: 11.4
     * @param {string} tabName - Name of the tab that was switched to
     */
    showTabSwitchFeedback(tabName) {
        const tabButton = document.querySelector(`.tab-btn[data-tab="${tabName}"]`);
        const tabPanel = document.querySelector(`#${tabName}-tab`);

        if (!tabButton) return;

        // Add ripple effect to tab button
        this.createRippleEffect(tabButton);

        // Add content loading animation
        if (tabPanel) {
            tabPanel.style.opacity = '0';
            tabPanel.style.transform = 'translateX(20px)';

            // Force reflow
            tabPanel.offsetHeight;

            // Animate in
            tabPanel.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
            tabPanel.style.opacity = '1';
            tabPanel.style.transform = 'translateX(0)';
        }
    }

    /**
     * Create ripple effect on element
     * Requirements: 11.4
     * @param {HTMLElement} element - Element to add ripple effect to
     */
    createRippleEffect(element) {
        const ripple = document.createElement('span');
        const rect = element.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);

        ripple.style.width = ripple.style.height = size + 'px';
        ripple.style.left = (rect.width / 2 - size / 2) + 'px';
        ripple.style.top = (rect.height / 2 - size / 2) + 'px';
        ripple.classList.add('ripple-effect');

        // Add ripple styles
        Object.assign(ripple.style, {
            position: 'absolute',
            borderRadius: '50%',
            background: 'rgba(243, 156, 18, 0.3)',
            transform: 'scale(0)',
            animation: 'ripple 0.6s linear',
            pointerEvents: 'none'
        });

        element.style.position = 'relative';
        element.style.overflow = 'hidden';
        element.appendChild(ripple);

        // Remove ripple after animation
        setTimeout(() => {
            if (ripple.parentNode) {
                ripple.parentNode.removeChild(ripple);
            }
        }, 600);
    }

    /**
     * Enhanced visual feedback for button interactions
     * Requirements: 11.4
     * @param {HTMLElement} button - Button element
     * @param {string} feedbackType - Type of feedback ('success', 'error', 'info')
     */
    showButtonFeedback(button, feedbackType = 'success') {
        if (!button) return;

        // Create ripple effect
        this.createRippleEffect(button);

        // Add feedback class
        const feedbackClass = `feedback-${feedbackType}`;
        button.classList.add(feedbackClass);

        // Remove feedback class after animation
        setTimeout(() => {
            button.classList.remove(feedbackClass);
        }, 300);
    }

    /**
     * Update event displays in the Events tab
     * Requirements: 9.1, 11.1, 11.3
     */
    updateEventDisplays() {
        if (!this.gameEngine || !this.elements.eventList) return;

        let eventHTML = '';

        // Get active events
        const activeEvents = this.gameEngine.eventManager ?
            this.gameEngine.eventManager.getActiveEvents() : [];

        // Get event history
        const eventHistory = this.gameEngine.eventManager ?
            this.gameEngine.eventManager.eventHistory : [];

        // Display active events section
        eventHTML += '<div class="event-section">';
        eventHTML += '<h4 class="event-section-title">Active Events</h4>';

        if (activeEvents.length > 0) {
            eventHTML += '<div class="active-events">';

            activeEvents.forEach(event => {
                const timeRemaining = event.duration ?
                    Math.max(0, event.duration - (Date.now() - event.startTime)) : 0;

                eventHTML += `
                    <div class="event-card active-event">
                        <div class="event-header">
                            <h5 class="event-name">${event.name}</h5>
                            <span class="event-category ${event.category}">${this.formatEventCategory(event.category)}</span>
                        </div>
                        <div class="event-description">${event.description}</div>
                        ${timeRemaining > 0 ? `
                            <div class="event-timer">
                                <strong>Time Remaining:</strong> ${TimeUtils.formatTime(timeRemaining)}
                            </div>
                        ` : ''}
                        ${event.effects ? `
                            <div class="event-effects">
                                <strong>Current Effects:</strong> ${this.formatEventEffects(event.effects)}
                            </div>
                        ` : ''}
                        <div class="event-actions">
                            <button class="event-details-btn" data-event-id="${event.id}">
                                View Details
                            </button>
                        </div>
                    </div>
                `;
            });

            eventHTML += '</div>';
        } else {
            eventHTML += '<p class="no-events">No active events at this time.</p>';
        }

        eventHTML += '</div>';

        // Display event history section
        eventHTML += '<div class="event-section">';
        eventHTML += '<h4 class="event-section-title">Recent Event History</h4>';

        if (eventHistory.length > 0) {
            eventHTML += '<div class="event-history">';

            // Show last 10 events
            const recentEvents = eventHistory.slice(-10).reverse();

            recentEvents.forEach(historyEvent => {
                const timeAgo = Date.now() - historyEvent.timestamp;
                const statusClass = historyEvent.resolved ? 'resolved' :
                                  historyEvent.expired ? 'expired' : 'pending';

                eventHTML += `
                    <div class="event-card history-event ${statusClass}">
                        <div class="event-header">
                            <h6 class="event-name">${historyEvent.name}</h6>
                            <span class="event-status ${statusClass}">${this.formatEventStatus(statusClass)}</span>
                        </div>
                        <div class="event-timestamp">
                            ${TimeUtils.formatTimeAgo(timeAgo)}
                        </div>
                    </div>
                `;
            });

            eventHTML += '</div>';
        } else {
            eventHTML += '<p class="no-events">No event history yet.</p>';
        }

        eventHTML += '</div>';

        DOMUtils.setHTML(this.elements.eventList, eventHTML);

        // Setup event listeners for event detail buttons
        this.setupEventButtons();
    }

    /**
     * Format event category for display
     * @param {string} category - Event category
     * @returns {string} - Formatted category name
     */
    formatEventCategory(category) {
        const categoryNames = {
            'localCrisis': 'Local Crisis',
            'internationalPolitics': 'International',
            'imfLoan': 'IMF Loan',
            'technology': 'Technology',
            'economic': 'Economic'
        };
        return categoryNames[category] || category;
    }

    /**
     * Format event status for display
     * @param {string} status - Event status
     * @returns {string} - Formatted status name
     */
    formatEventStatus(status) {
        const statusNames = {
            'resolved': 'Resolved',
            'expired': 'Expired',
            'pending': 'Pending',
            'active': 'Active'
        };
        return statusNames[status] || status;
    }

    /**
     * Format event effects for display
     * @param {Object} effects - Event effects object
     * @returns {string} - Formatted effects description
     */
    formatEventEffects(effects) {
        if (!effects) return 'None';

        const effectStrings = [];

        if (effects.resourceReduction) {
            for (const [resource, multiplier] of Object.entries(effects.resourceReduction)) {
                const percentage = Math.round((1 - multiplier) * 100);
                effectStrings.push(`${resource} -${percentage}%`);
            }
        }

        if (effects.resourceMultiplier) {
            for (const [resource, multiplier] of Object.entries(effects.resourceMultiplier)) {
                const percentage = Math.round((multiplier - 1) * 100);
                if (percentage > 0) {
                    effectStrings.push(`${resource} +${percentage}%`);
                } else if (percentage < 0) {
                    effectStrings.push(`${resource} ${percentage}%`);
                }
            }
        }

        return effectStrings.length > 0 ? effectStrings.join(', ') : 'Various effects';
    }

    /**
     * Setup event listeners for event buttons
     * Requirements: 9.1, 11.1
     */
    setupEventButtons() {
        const eventDetailButtons = document.querySelectorAll('.event-details-btn');

        eventDetailButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                const eventId = e.target.getAttribute('data-event-id');
                if (eventId) {
                    this.showEventDetails(eventId);
                }
            });
        });
    }

    /**
     * Show detailed information about an event
     * @param {string} eventId - ID of the event to show details for
     */
    showEventDetails(eventId) {
        if (!this.gameEngine.eventManager) return;

        const event = this.gameEngine.eventManager.getActiveEvents()
            .find(e => e.id === eventId);

        if (event) {
            this.showEventModal(event);
        }
    }

    /**
     * Format choice cost for display
     * @param {Object} cost - Cost object
     * @returns {string} - Formatted cost string
     */
    formatChoiceCost(cost) {
        const costParts = [];

        for (const [resource, amount] of Object.entries(cost)) {
            if (resource === 'gdp') {
                costParts.push(`$${NumberFormatter.format(amount)} GDP`);
            } else {
                const resourceName = resource.charAt(0).toUpperCase() + resource.slice(1);
                costParts.push(`${NumberFormatter.format(amount)} ${resourceName}`);
            }
        }

        return costParts.join(', ');
    }

    /**
     * Check if player can afford a choice
     * @param {Object} choice - Choice to check
     * @returns {boolean} - Whether player can afford the choice
     */
    canPlayerAffordChoice(choice) {
        if (!choice.cost || Object.keys(choice.cost).length === 0) {
            return true;
        }

        const gameState = this.gameEngine.getGameState();

        for (const [resource, amount] of Object.entries(choice.cost)) {
            if (resource === 'gdp') {
                if (gameState.gdp < amount) return false;
            } else if (gameState.resources[resource] < amount) {
                return false;
            }
        }

        return true;
    }

    /**
     * Show loading screen
     */
    showLoadingScreen() {
        if (this.elements.loadingScreen) {
            this.elements.loadingScreen.classList.remove('hidden');
        }
    }
    
    /**
     * Hide loading screen
     */
    hideLoadingScreen() {
        if (this.elements.loadingScreen) {
            this.elements.loadingScreen.classList.add('hidden');
        }
    }

    /**
     * Cleanup UI Manager
     */
    cleanup() {
        this.stopPeriodicUpdates();
        console.log('UIManager: Cleaned up');
    }
}

// Export for global access
window.UIManager = UIManager;

// Export for potential module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = UIManager;
}